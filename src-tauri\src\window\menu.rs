
use anyhow::Result;
use lazy_static::lazy_static;
use serde_json::json;
use std::collections::HashMap;
use tauri::async_runtime::spawn;
use tauri::menu::{
    AboutMetadata, CheckMenuItem, IsMenuItem, Menu, MenuId, MenuItem, MenuItemBuilder, PredefinedMenuItem,
    Submenu, HELP_SUBMENU_ID,
};
use tauri::{Emitter, Manager, State, WebviewWindow, Wry};

// Import both sets of dependencies
use crate::cmd::window::{create_document_window, NewWindowOptionsBuilder};
use crate::global_config::{AppConfig, Plan};
use crate::edit1::{create_new_editor, AppState};
use crate::editor::open_files;

pub const WINDOW_SUBMENU_ID: &str = "__WINDOW_SUBMENU__";

// Hashmap for window menu items
// Where key is window label and value is menu item ID
lazy_static! {
    pub static ref WINDOW_MENU_ITEMS_MAP: HashMap<String, String> = HashMap::new();
}

#[derive(Debug, Clone)]
pub enum MenuType {
    Document,
    Table { insert_id: String },
    Editor,
}

pub fn get_focused_window(app_handle: &tauri::AppHandle) -> Option<tauri::WebviewWindow> {
    for window in app_handle.webview_windows().values() {
        if let Ok(true) = window.is_focused() {
            return Some(window.clone());
        }
    }
    None
}

fn setup_window_menu(app_handle: &tauri::AppHandle) -> Submenu<Wry> {
    Submenu::with_id_and_items(
        app_handle,
        WINDOW_SUBMENU_ID,
        "Window",
        true,
        &[
            &PredefinedMenuItem::minimize(app_handle, None).unwrap(),
            &PredefinedMenuItem::maximize(app_handle, None).unwrap(),
            &PredefinedMenuItem::separator(app_handle).unwrap(),
            &PredefinedMenuItem::close_window(app_handle, None).unwrap(),
            &PredefinedMenuItem::separator(app_handle).unwrap(),
        ],
    )
    .unwrap()
}

/// Unified menu creation function that handles all menu types
pub fn create_menu(
    app_handle: tauri::AppHandle,
    menu_type: MenuType,
    from_setup: bool,
) -> Result<tauri::menu::Menu<Wry>> {
    let window_menu_map: HashMap<String, MenuId> = HashMap::new(); // label: itemID
    app_handle.manage(std::sync::Mutex::new(window_menu_map));

    let pkg_info = app_handle.package_info();
    let config = app_handle.config();

    let about_metadata = AboutMetadata {
        name: Some(pkg_info.name.clone()),
        version: Some(pkg_info.version.to_string()),
        copyright: config.bundle.copyright.clone(),
        authors: config.bundle.publisher.clone().map(|p| vec![p]),
        ..Default::default()
    };

    let window_menu = setup_window_menu(&app_handle);

    // Create help menu based on menu type
    let help_item = match menu_type {
        MenuType::Editor => MenuItem::new(&app_handle, "Edit1 Help", true, Some("")).unwrap(),
        _ => MenuItem::new(&app_handle, "Edit Deeply Help", true, Some("")).unwrap(),
    };

    let help_menu = Submenu::with_id_and_items(&app_handle, HELP_SUBMENU_ID, "Help", true, &[&help_item])?;

    // Create menu items based on type
    let (file_items, edit_items, view_items, app_menu_items) = match &menu_type {
        MenuType::Document | MenuType::Table { .. } => {
            // Document/Table menu items
            let preferences_item = MenuItem::new(&app_handle, "Preferences…", true, Some("Cmd+,")).unwrap();
            let new_editor = MenuItem::new(&app_handle, "New", true, Some("")).unwrap();
            let open_editor = MenuItem::new(&app_handle, "Open", true, Some("")).unwrap();

            let file_items: Vec<Box<dyn IsMenuItem<Wry>>> = vec![
                Box::new(new_editor.clone()),
                Box::new(open_editor.clone()),
                Box::new(PredefinedMenuItem::separator(&app_handle).unwrap()),
                Box::new(PredefinedMenuItem::close_window(&app_handle, None).unwrap()),
            ];

            let insert_menu_item = match &menu_type {
                MenuType::Document => {
                    MenuItemBuilder::new("New Row")
                        .id("global_insert")
                        .accelerator("CommandOrControl+N")
                        .build(&app_handle)
                        .unwrap()
                }
                MenuType::Table { insert_id } => {
                    MenuItemBuilder::new("New Row")
                        .id(insert_id.clone())
                        .accelerator("CommandOrControl+N")
                        .build(&app_handle)
                        .unwrap()
                }
                _ => unreachable!(),
            };

            let edit_items: Vec<Box<dyn IsMenuItem<Wry>>> = vec![
                Box::new(PredefinedMenuItem::undo(&app_handle, None).unwrap()),
                Box::new(PredefinedMenuItem::redo(&app_handle, None).unwrap()),
                Box::new(PredefinedMenuItem::separator(&app_handle).unwrap()),
                Box::new(PredefinedMenuItem::cut(&app_handle, None).unwrap()),
                Box::new(PredefinedMenuItem::copy(&app_handle, None).unwrap()),
                Box::new(PredefinedMenuItem::paste(&app_handle, None).unwrap()),
                Box::new(PredefinedMenuItem::select_all(&app_handle, None).unwrap()),
                Box::new(PredefinedMenuItem::separator(&app_handle).unwrap()),
                Box::new(insert_menu_item),
            ];

            let app_menu_items: Vec<Box<dyn IsMenuItem<Wry>>> = vec![
                Box::new(PredefinedMenuItem::about(&app_handle, None, Some(about_metadata.clone())).unwrap()),
                Box::new(preferences_item),
                Box::new(PredefinedMenuItem::separator(&app_handle).unwrap()),
                Box::new(PredefinedMenuItem::hide(&app_handle, None).unwrap()),
                Box::new(PredefinedMenuItem::hide_others(&app_handle, None).unwrap()),
                Box::new(PredefinedMenuItem::separator(&app_handle).unwrap()),
                Box::new(PredefinedMenuItem::quit(&app_handle, None).unwrap()),
            ];

            (file_items, edit_items, None, app_menu_items)
        }
        MenuType::Editor => {
            // Editor menu items with explicit IDs
            let new_item = MenuItemBuilder::new("New")
                .id("new")
                .accelerator("CommandOrControl+N")
                .build(&app_handle)
                .unwrap();
            let open_new_item = MenuItemBuilder::new("Open…")
                .id("open")
                .accelerator("CmdOrCtrl+o")
                .build(&app_handle)
                .unwrap();
            let save_item = MenuItemBuilder::new("Save")
                .id("save")
                .accelerator("CmdOrCtrl+s")
                .build(&app_handle)
                .unwrap();
            let save_as_item = MenuItemBuilder::new("Save As…")
                .id("save_as")
                .accelerator("CmdOrCtrl+Shift+s")
                .build(&app_handle)
                .unwrap();
            let split_item = CheckMenuItem::with_id(&app_handle, "split_view", "Split view", true, false, Some("")).unwrap();
            let dark_item = CheckMenuItem::with_id(&app_handle, "dark_mode", "Dark Mode", true, false, Some("")).unwrap();

            let file_items: Vec<Box<dyn IsMenuItem<Wry>>> = vec![
                Box::new(new_item.clone()),
                Box::new(open_new_item.clone()),
                Box::new(save_item.clone()),
                Box::new(save_as_item.clone()),
                Box::new(PredefinedMenuItem::separator(&app_handle).unwrap()),
                Box::new(split_item.clone()),
            ];

            let edit_items: Vec<Box<dyn IsMenuItem<Wry>>> = vec![
                Box::new(PredefinedMenuItem::undo(&app_handle, None).unwrap()),
                Box::new(PredefinedMenuItem::redo(&app_handle, None).unwrap()),
                Box::new(PredefinedMenuItem::separator(&app_handle).unwrap()),
                Box::new(PredefinedMenuItem::cut(&app_handle, None).unwrap()),
                Box::new(PredefinedMenuItem::copy(&app_handle, None).unwrap()),
                Box::new(PredefinedMenuItem::paste(&app_handle, None).unwrap()),
                Box::new(PredefinedMenuItem::select_all(&app_handle, None).unwrap()),
                Box::new(PredefinedMenuItem::separator(&app_handle).unwrap()),
                Box::new(MenuItemBuilder::new("Insert")
                    .id("global_insert")
                    .accelerator("CommandOrControl+I")
                    .build(&app_handle)
                    .unwrap()),
            ];

            let view_items: Vec<Box<dyn IsMenuItem<Wry>>> = vec![
                Box::new(PredefinedMenuItem::fullscreen(&app_handle, None).unwrap()),
                Box::new(split_item.clone()),
                Box::new(dark_item.clone()),
            ];

            let app_menu_items: Vec<Box<dyn IsMenuItem<Wry>>> = vec![
                Box::new(PredefinedMenuItem::about(&app_handle, None, Some(about_metadata.clone())).unwrap()),
                Box::new(PredefinedMenuItem::separator(&app_handle).unwrap()),
                Box::new(PredefinedMenuItem::hide(&app_handle, None).unwrap()),
                Box::new(PredefinedMenuItem::hide_others(&app_handle, None).unwrap()),
                Box::new(PredefinedMenuItem::separator(&app_handle).unwrap()),
                Box::new(PredefinedMenuItem::quit(&app_handle, None).unwrap()),
            ];

            (file_items, edit_items, Some(view_items), app_menu_items)
        }
    };

    // Convert items to references
    let file_items: Vec<&dyn IsMenuItem<Wry>> = file_items
        .iter()
        .map(|a| a.as_ref() as &dyn IsMenuItem<Wry>)
        .collect();

    let edit_items: Vec<&dyn IsMenuItem<Wry>> = edit_items
        .iter()
        .map(|a| a.as_ref() as &dyn IsMenuItem<Wry>)
        .collect();

    let app_menu_items: Vec<&dyn IsMenuItem<Wry>> = app_menu_items
        .iter()
        .map(|a| a.as_ref() as &dyn IsMenuItem<Wry>)
        .collect();

    // Convert view_items if it exists
    let view_items_refs: Option<Vec<&dyn IsMenuItem<Wry>>> = view_items.as_ref().map(|items| {
        items
            .iter()
            .map(|a| a.as_ref() as &dyn IsMenuItem<Wry>)
            .collect()
    });

    // Build the menu - create submenus first to avoid temporary value issues
    let app_submenu = Submenu::with_items(&app_handle, pkg_info.name.clone(), true, &app_menu_items)?;
    let file_submenu = Submenu::with_items(&app_handle, "File", true, &file_items)?;
    let edit_submenu = Submenu::with_items(&app_handle, "Edit", true, &edit_items)?;

    let mut menu_items: Vec<&dyn IsMenuItem<Wry>> = vec![
        &app_submenu,
        &file_submenu,
        &edit_submenu,
    ];

    // Add View menu if it exists (for Editor type)
    let view_submenu = if let Some(view_items_refs) = view_items_refs {
        Some(Submenu::with_items(&app_handle, "View", true, &view_items_refs)?)
    } else {
        None
    };

    if let Some(ref view_submenu) = view_submenu {
        menu_items.push(view_submenu);
    }

    // Add Window and Help menus
    menu_items.push(&window_menu);
    menu_items.push(&help_menu);

    let menu = Menu::with_items(&app_handle, &menu_items)?;

    if from_setup {
        menu.set_as_app_menu().unwrap();
    }

    // Set up event handlers based on menu type
    match menu_type {
        MenuType::Document => setup_document_event_handler(app_handle, help_item),
        MenuType::Table { insert_id } => setup_table_event_handler(app_handle, help_item, insert_id),
        MenuType::Editor => setup_editor_event_handler(app_handle),
    }

    Ok(menu)
}

/// Common handler for preferences menu item
fn handle_preferences(app_handle: &tauri::AppHandle) {
    let app_config: State<'_, AppConfig> = app_handle.state();
    let app_name = app_handle.config().product_name.clone().unwrap().to_lowercase().replace(" ", "_");
    let help_page = format!("/windows/help_{}.html", app_name);

    match app_config.plan {
        Plan::Free => {
            let app_handle = app_handle.clone();
            spawn(create_document_window(
                app_handle,
                "Help".into(),
                "help".into(),
                help_page.into(),
                NewWindowOptionsBuilder::default().build().unwrap(),
            ));
        }
        _ => {
            let app_handle = app_handle.clone();
            spawn(create_document_window(
                app_handle,
                "Settings".into(),
                "settings".into(),
                "/windows/settings.html".into(),
                NewWindowOptionsBuilder::default().build().unwrap(),
            ));
        }
    }
}

/// Common handler for help menu item
fn handle_help(app_handle: &tauri::AppHandle, help_item: &MenuItem<Wry>) {
    let app_name = app_handle.config().product_name.clone().unwrap().to_lowercase().replace(" ", "_");
    let help_page = format!("/windows/help_{}.html", app_name);

    let app_handle = app_handle.clone();
    spawn(create_document_window(
        app_handle,
        "Help".into(),
        "help".into(),
        help_page.into(),
        NewWindowOptionsBuilder::default().build().unwrap(),
    ));
}

fn setup_document_event_handler(app_handle: tauri::AppHandle, help_item: MenuItem<Wry>) {
    app_handle.on_menu_event(move |app_handle, event| {
        let id = event.id();

        // Handle preferences
        if id.0 == "preferences" {
            handle_preferences(app_handle);
        }
        // Handle help
        else if id == help_item.id() {
            handle_help(app_handle, &help_item);
        }
        // Handle global insert
        else if id.0 == "global_insert" {
            let focused_window = app_handle.get_focused_window();
            if focused_window.is_some() {
                let _ = app_handle.emit("add_new", json!({"label": focused_window.unwrap().label()}));
            }
        }
        // Handle window focus
        else {
            maybe_focus_window(id, app_handle);
        }
    });
}

fn setup_table_event_handler(app_handle: tauri::AppHandle, help_item: MenuItem<Wry>, insert_id: String) {
    app_handle.on_menu_event(move |app_handle, event| {
        let id = event.id();

        // Handle preferences
        if id.0 == "preferences" {
            handle_preferences(app_handle);
        }
        // Handle help
        else if id == help_item.id() {
            handle_help(app_handle, &help_item);
        }
        // Handle table-specific insert
        else if id.0.starts_with("insert_") {
            let _ = app_handle.emit("add_new", json!({"insert_id": id.0}));
        }
        // Handle window focus
        else {
            maybe_focus_window(id, app_handle);
        }
    });
}

fn setup_editor_event_handler(app_handle: tauri::AppHandle) {
    app_handle.on_menu_event(move |app_handle, event| {
        let id = event.id();
        println!("Menu event triggered: {:?}", id);

        let state: State<'_, AppState> = app_handle.state();
        let current_window = state.active_window.clone();
        let window_guard = current_window.as_ref().lock().unwrap();

        // Handle menu items
        if id.0 == "new" {
            let app_handle_clone = app_handle.clone();
            spawn(create_new_editor(app_handle_clone.to_owned(), None));
        } else if id.0 == "open" {
            let app_handle_clone = app_handle.clone();
            tauri::async_runtime::spawn(async move {
                let _ = open_files(app_handle_clone).await;
            });
        } else if id.0 == "save" {
            if let Some(w) = &*window_guard {
                let _ = w.emit_to(w.label(), "file:save", "");
            }
        } else if id.0 == "save_as" {
            if let Some(w) = &*window_guard {
                let _ = w.emit_to(w.label(), "file:save-as", "");
            }
        } else if id.0 == "split_view" {
            if let Some(w) = &*window_guard {
                // Note: We'd need to get the actual checked state from the menu item
                let _ = w.emit_to(w.label(), "file:split", json!({"split": true}));
            }
        } else if id.0 == "dark_mode" {
            if let Some(w) = &*window_guard {
                // Note: We'd need to get the actual checked state from the menu item
                let _ = w.emit("theme:change", json!({"dark": true}));
            }
        }
    });
}

/// Check if current menu item exists in menu window map
/// If it found focus the window
fn maybe_focus_window(id: &MenuId, app_handle: &tauri::AppHandle) {
    // Handle dynamic window menu items
    let menu_map: State<std::sync::Mutex<HashMap<String, MenuId>>> = app_handle.state();
    let menu_map = menu_map.lock().unwrap();
    for (label, item_id) in menu_map.iter() {
        if id == item_id {
            match app_handle.get_window(label) {
                Some(window) => {
                    window.unminimize().unwrap();
                    window.set_focus().unwrap();
                }
                None => {
                    log::error!("Not found window to focus {}", label);
                }
            }
        }
    }
}

pub fn update_titles(app_handle: &tauri::AppHandle) {
    let menu = app_handle.menu().unwrap();
    let window_submenu = menu.get(WINDOW_SUBMENU_ID).unwrap();
    let window_submenu = window_submenu.as_submenu().unwrap();
    let menu_map: State<std::sync::Mutex<HashMap<String, MenuId>>> = app_handle.state();
    let mut menu_map = menu_map.lock().unwrap();

    // Remove window menu and clear menu map
    menu_map.clear();

    let items = window_submenu.items().unwrap();
    for item in items.iter().skip(5) {
        window_submenu.remove(item).unwrap();
    }
    // Rebuild window items
    let windows = app_handle.webview_windows();

    // Collect windows and their titles into a vector
    let mut window_vec: Vec<(&String, &WebviewWindow)> = windows.iter().collect();

    // Sort the vector by the titles
    window_vec.sort_by(|a, b| {
        a.1.title()
            .unwrap_or_default()
            .cmp(&b.1.title().unwrap_or_default())
    });

    for (label, window) in window_vec.iter() {
        let title = window.title().unwrap_or("Unknown".into());
        let item: Box<dyn IsMenuItem<tauri::Wry>> =
            Box::new(MenuItem::new(app_handle, title, true, Some("")).unwrap());
        menu_map.insert(label.to_string(), item.id().clone());
        window_submenu.append(&*item).unwrap();
    }
}

// Legacy function wrappers for backward compatibility
pub fn create_menu_document(
    app_handle: tauri::AppHandle,
    from_setup: bool,
) -> Result<tauri::menu::Menu<Wry>> {
    create_menu(app_handle, MenuType::Document, from_setup)
}

pub fn create_menu_table(
    app_handle: tauri::AppHandle,
    insert_id: String,
) -> Result<tauri::menu::Menu<Wry>> {
    create_menu(app_handle, MenuType::Table { insert_id }, false)
}

pub fn create_menu_editor(
    app_handle: tauri::AppHandle,
    from_setup: bool,
) -> Result<tauri::menu::Menu<Wry>> {
    create_menu(app_handle, MenuType::Editor, from_setup)
}
